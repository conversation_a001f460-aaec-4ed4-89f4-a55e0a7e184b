import React, { useState } from "react";
import { Controller } from "react-hook-form";
import { Upload, X } from "lucide-react";
import PriorityDropdown from "../components/PriorityDropdown";
import LabelSelector from "../components/LabelSelector";
import Calendar from "../components/Calendar";
import TimePicker from "../components/TimePicker";
import TowerSelector from "../../Announcements/components/TowerSelector";
import UnitSelector from "../../Announcements/components/UnitSelector";
import PostAsSelector from "../components/PostAsSelector";
import ErrorMessage from "../../../../Components/MessageBox/ErrorMessage";
import LoadingAnimation from "../../../../Components/Loaders/LoadingAnimation";

/**
 * EditNoticeForm Component
 * Comprehensive form for editing notices with sectioned design and advanced functionality
 */
const EditNoticeForm = ({
  // Form props
  control,
  handleSubmit,
  watch,
  setValue,
  errors,
  isSubmitting,
  onSubmit,

  // State props
  currentUser,
  attachments,
  notice,
  formChanged,

  // Error states
  fileUploadError,
  dateOrderError,

  // Handlers
  handleFileUpload,
  removeAttachment,
  handleMemberSelect,
  handleGroupSelect,
  isFormValid,

  // Watched values
  watchedValues,
  selectedTowers
}) => {
  const [showImagePreview, setShowImagePreview] = useState(false);
  const [previewImage, setPreviewImage] = useState(null);

  // Handle image preview
  const handleImagePreview = (attachment) => {
    setPreviewImage(attachment);
    setShowImagePreview(true);
  };

  // Close preview modal
  const closeImagePreview = () => {
    setShowImagePreview(false);
    setPreviewImage(null);
  };

  return (
    <div className="relative">
      {/* Loading Overlay */}
      {isSubmitting && (
        <div className="absolute inset-0 bg-white bg-opacity-75 flex items-center justify-center z-50 rounded-lg">
          <LoadingAnimation />
        </div>
      )}

      <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
        {/* Notice Author Section */}
        <div className="bg-white rounded-lg shadow-sm p-6">
          <h3 className="text-lg font-semibold text-primary mb-4">Notice Author</h3>

          {/* Creator Name and Post as on different rows */}
          <div className="space-y-4">
            {/* Creator Name */}
            <div>
              <label className="block text-sm font-semibold text-gray-700 mb-2">
                Creator Name
              </label>
              <Controller
                name="creatorName"
                control={control}
                render={({ field }) => (
                  <input
                    {...field}
                    type="text"
                    readOnly
                    className="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50 text-gray-700 cursor-not-allowed"
                    value={currentUser?.full_name || currentUser?.fullName || 'Current User'}
                  />
                )}
              />
            </div>

            {/* Post as */}
            <div>
              <div className="flex items-center mb-3">
                <label className="block text-sm font-semibold text-gray-700">
                  Post as <span className="text-primary">*</span>
                </label>
                <div className="ml-8">
                  <Controller
                    name="postAs"
                    control={control}
                    render={({ field }) => (
                      <div className="flex space-x-6">
                        <label className={`flex items-center ${notice?.post_as === 'creator' ? 'cursor-pointer group' : 'cursor-not-allowed opacity-50'}`}>
                          <div className="relative">
                            <input
                              type="radio"
                              {...field}
                              value="Creator"
                              checked={field.value === 'Creator'}
                              onChange={(e) => {
                                if (notice?.post_as === 'creator') {
                                  field.onChange(e.target.value);
                                  // Clear member and group selections when switching to Creator
                                  setValue('selectedMemberId', '');
                                  setValue('selectedMemberName', '');
                                  setValue('selectedGroupId', '');
                                  setValue('selectedGroupName', '');
                                  // Set creator name to current user
                                  const user = currentUser || (() => {
                                    try {
                                      const member = localStorage.getItem('member');
                                      return member ? JSON.parse(member) : null;
                                    } catch (error) {
                                      return null;
                                    }
                                  })();
                                  if (user) {
                                    setValue('creatorName', user.full_name || user.fullName || 'Current User');
                                  }
                                }
                              }}
                              disabled={notice?.post_as !== 'creator'}
                              className="sr-only peer"
                            />
                            <div className="w-4 h-4 border-2 border-gray-300 rounded-full peer-checked:border-primary peer-checked:border-4"></div>
                          </div>
                          <span className="ml-2 text-sm text-gray-700">Creator</span>
                        </label>
                        <label className={`flex items-center ${notice?.post_as === 'group' ? 'cursor-pointer group' : 'cursor-not-allowed opacity-50'}`}>
                          <div className="relative">
                            <input
                              type="radio"
                              {...field}
                              value="Group"
                              checked={field.value === 'Group'}
                              onChange={(e) => {
                                if (notice?.post_as === 'group') {
                                  field.onChange(e.target.value);
                                  // Clear member selection when switching to Group
                                  setValue('selectedMemberId', '');
                                  setValue('selectedMemberName', '');
                                  // Clear group selection to allow fresh selection
                                  setValue('selectedGroupId', '');
                                  setValue('selectedGroupName', '');
                                  // Set creator name to current user when switching to Group
                                  const user = currentUser || (() => {
                                    try {
                                      const member = localStorage.getItem('member');
                                      return member ? JSON.parse(member) : null;
                                    } catch (error) {
                                      return null;
                                    }
                                  })();
                                  if (user) {
                                    setValue('creatorName', user.full_name || user.fullName || 'Current User');
                                  }
                                }
                              }}
                              disabled={notice?.post_as !== 'group'}
                              className="sr-only peer"
                            />
                            <div className="w-4 h-4 border-2 border-gray-300 rounded-full peer-checked:border-primary peer-checked:border-4"></div>
                          </div>
                          <span className="ml-2 text-sm text-gray-700">Group</span>
                        </label>
                        <label className={`flex items-center ${notice?.post_as === 'member' ? 'cursor-pointer group' : 'cursor-not-allowed opacity-50'}`}>
                          <div className="relative">
                            <input
                              type="radio"
                              {...field}
                              value="Member"
                              checked={field.value === 'Member'}
                              onChange={(e) => {
                                if (notice?.post_as === 'member') {
                                  field.onChange(e.target.value);
                                  // Clear member and group selections when switching to Member
                                  setValue('selectedMemberId', '');
                                  setValue('selectedMemberName', '');
                                  setValue('selectedGroupId', '');
                                  setValue('selectedGroupName', '');
                                  // Set creator name to current user when switching to Member (like Group)
                                  const user = currentUser || (() => {
                                    try {
                                      const member = localStorage.getItem('member');
                                      return member ? JSON.parse(member) : null;
                                    } catch (error) {
                                      return null;
                                    }
                                  })();
                                  if (user) {
                                    setValue('creatorName', user.full_name || user.fullName || 'Current User');
                                  }
                                }
                              }}
                              disabled={notice?.post_as !== 'member'}
                              className="sr-only peer"
                            />
                            <div className="w-4 h-4 border-2 border-gray-300 rounded-full peer-checked:border-primary peer-checked:border-4"></div>
                          </div>
                          <span className="ml-2 text-sm text-gray-700">Member</span>
                        </label>
                      </div>
                    )}
                  />
                </div>
              </div>
              {errors.postAs && (
                <p className="mt-1 text-sm text-red-600">{errors.postAs.message}</p>
              )}
            </div>

            {/* PostAsSelector Integration for Group/Member Selection */}
            {(watchedValues.postAs === 'Group' || watchedValues.postAs === 'Member') && (
              <div>
                <PostAsSelector
                  value={watchedValues.postAs}
                  selectedGroup={watchedValues.selectedGroupId}
                  selectedMember={watchedValues.selectedMemberId}
                  onChange={(postAs, groupId, memberId) => {
                    handleGroupSelect(groupId ? { id: groupId, name: watchedValues.selectedGroupName } : null);
                    handleMemberSelect(memberId ? { id: memberId, name: watchedValues.selectedMemberName } : null);
                  }}
                  currentUser={currentUser}
                  error={errors.selectedGroupId?.message || errors.selectedMemberId?.message}
                  disabled={true} // Disable during editing
                />
              </div>
            )}

            {/* Auto Name Field - Only show when Creator is selected */}
            {watchedValues.postAs === 'Creator' && (
              <div>
                <Controller
                  name="autoName"
                  control={control}
                  render={({ field }) => (
                    <input
                      {...field}
                      type="text"
                      disabled
                      className="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50 cursor-not-allowed"
                      placeholder="Auto-filled from Creator Name"
                    />
                  )}
                />
              </div>
            )}
          </div>
        </div>

        {/* Attachments Section */}
        <div className="bg-white rounded-lg shadow-sm p-6">
          <label className="block text-sm font-semibold text-gray-700 mb-2">
            Attachments
          </label>
            <div className="border-2 border-dashed border-gray-300 rounded-lg p-4">
              <input
                type="file"
                multiple
                accept="image/*"
                onChange={handleFileUpload}
                className="hidden"
                id="edit-notice-file-upload"
              />
              <label
                htmlFor="edit-notice-file-upload"
                className="cursor-pointer flex flex-col items-center"
              >
                <Upload className="w-8 h-8 text-gray-400 mb-2" />
                <span className="text-sm text-gray-600">Click to upload images</span>
              </label>
            </div>

            {/* Error Message */}
            <ErrorMessage message={fileUploadError} />

            {/* Display uploaded attachments */}
            {attachments.length > 0 && (
              <div className="mt-3">
                <div className="flex items-center justify-between mb-2">
                  {/* <span className="text-sm font-medium text-gray-700">
                    Uploaded Attachments ({attachments.length}/10)
                  </span> */}
                </div>
                <div className="grid grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-2">
                  {attachments.map((attachment) => (
                    <div key={attachment.id} className="relative group">
                      <img
                        src={attachment.url || attachment.base64 || attachment.preview}
                        alt={attachment.name || attachment.file_name}
                        className="w-full h-20 object-cover rounded border cursor-pointer hover:opacity-75 transition-opacity"
                        onClick={() => handleImagePreview(attachment)}
                        onError={(e) => {
                          e.target.style.display = 'none';
                          e.target.nextSibling.style.display = 'flex';
                        }}
                      />
                      {/* Fallback */}
                      <div className="hidden w-full h-20 bg-gray-100 rounded border items-center justify-center">
                        <span className="text-xs text-gray-500">Image</span>
                      </div>

                      {/* Remove Button */}
                      <button
                        type="button"
                        onClick={() => removeAttachment(attachment.id)}
                        className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full w-5 h-5 flex items-center justify-center text-xs opacity-0 group-hover:opacity-100 transition-opacity"
                      >
                        <X className="w-3 h-3" />
                      </button>
                    </div>
                  ))}
                </div>
              </div>
            )}
        </div>

        {/* Label and Priority Section */}
        <div className="bg-white rounded-lg shadow-sm p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Label */}
            <div>
              <Controller
                name="label"
                control={control}
                render={({ field }) => (
                  <LabelSelector
                    value={field.value}
                    onChange={field.onChange}
                    error={errors.label?.message}
                  />
                )}
              />
            </div>

            {/* Priority */}
            <div>
              <label className="block text-sm font-semibold text-gray-700 mb-2">
                Priority <span className="text-primary">*</span>
              </label>
              <Controller
                name="priority"
                control={control}
                render={({ field }) => (
                  <PriorityDropdown
                    value={field.value}
                    onChange={field.onChange}
                    error={errors.priority?.message}
                  />
                )}
              />
            </div>
          </div>
        </div>

        {/* Notice Visibility Section */}
        <div className="bg-white rounded-lg shadow-sm p-6">
          <h3 className="text-lg font-semibold text-primary mb-4">Notice Visibility</h3>

          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            {/* Start Date */}
            <div>
              <label className="block text-sm font-semibold text-gray-700 mb-2">
                Start Date <span className="text-primary">*</span>
              </label>
              <Controller
                name="startDate"
                control={control}
                render={({ field }) => (
                  <Calendar
                    value={field.value}
                    onChange={field.onChange}
                    placeholder="Select start date"
                  />
                )}
              />
              {errors.startDate && (
                <p className="mt-1 text-sm text-red-600">{errors.startDate.message}</p>
              )}
            </div>

            {/* Start Time */}
            <div>
              <label className="block text-sm font-semibold text-gray-700 mb-2">
                Start Time <span className="text-primary">*</span>
              </label>
              <Controller
                name="startTime"
                control={control}
                render={({ field }) => (
                  <TimePicker
                    value={field.value}
                    onChange={field.onChange}
                    placeholder="Select start time"
                  />
                )}
              />
              {errors.startTime && (
                <p className="mt-1 text-sm text-red-600">{errors.startTime.message}</p>
              )}
            </div>

            {/* End Date */}
            <div>
              <label className="block text-sm font-semibold text-gray-700 mb-2">
                End Date <span className="text-primary">*</span>
              </label>
              <Controller
                name="endDate"
                control={control}
                render={({ field }) => (
                  <Calendar
                    value={field.value}
                    onChange={field.onChange}
                    placeholder="Select end date"
                  />
                )}
              />
              {errors.endDate && (
                <p className="mt-1 text-sm text-red-600">{errors.endDate.message}</p>
              )}
            </div>

            {/* End Time */}
            <div>
              <label className="block text-sm font-semibold text-gray-700 mb-2">
                End Time <span className="text-primary">*</span>
              </label>
              <Controller
                name="endTime"
                control={control}
                render={({ field }) => (
                  <TimePicker
                    value={field.value}
                    onChange={field.onChange}
                    placeholder="Select end time"
                  />
                )}
              />
              {errors.endTime && (
                <p className="mt-1 text-sm text-red-600">{errors.endTime.message}</p>
              )}
            </div>
          </div>

          {/* Date/Time Validation Error */}
          {dateOrderError && (
            <ErrorMessage message={dateOrderError} />
          )}
        </div>

        {/* Tower and Unit Section */}
        <div className="bg-white rounded-lg shadow-sm p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Tower Selector */}
            <div>
              <Controller
                name="selectedTowers"
                control={control}
                render={({ field }) => (
                  <TowerSelector
                    value={field.value}
                    onChange={field.onChange}
                    error={errors.selectedTowers?.message}
                  />
                )}
              />
            </div>

            {/* Unit Selector */}
            <div>
              <Controller
                name="selectedUnits"
                control={control}
                render={({ field }) => (
                  <UnitSelector
                    value={field.value}
                    onChange={field.onChange}
                    selectedTowers={selectedTowers}
                    error={errors.selectedUnits?.message}
                    isEditing={true}
                  />
                )}
              />
            </div>
          </div>
        </div>

        {/* Submit Button */}
        <div className="flex justify-center">
          <button
            type="submit"
            disabled={isSubmitting || !formChanged}
            className={`w-full px-8 py-3 rounded-md transition duration-200 font-medium ${
              formChanged && !isSubmitting
                ? 'bg-primary text-white hover:bg-primaryHover cursor-pointer'
                : 'bg-white text-primary border-2 border-primary hover:bg-gray-50 cursor-not-allowed'
            } ${
              isSubmitting ? 'opacity-50' : ''
            }`}
          >
            {isSubmitting ? 'Updating...' : 'Send'}
          </button>
        </div>
      </form>

      {/* Image Preview Modal */}
      {showImagePreview && previewImage && (
        <div className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 p-4">
          <div className="relative max-w-4xl max-h-full">
            <button
              onClick={closeImagePreview}
              className="absolute top-4 right-4 bg-black bg-opacity-50 text-white rounded-full p-2 hover:bg-opacity-75 transition-colors z-10"
            >
              <X className="w-4 h-4" />
            </button>
            <img
              src={previewImage.url || previewImage.base64 || previewImage.preview}
              alt={previewImage.name}
              className="max-w-full max-h-full object-contain rounded-lg"
            />
            <div className="absolute bottom-4 left-4 bg-black bg-opacity-50 text-white px-3 py-2 rounded">
              {previewImage.name}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default EditNoticeForm;
